using AutoMapper;
using FluentAssertions;
using Microsoft.Extensions.Options;
using Moq;
using Workforce.Application.Features.Users.Commands.LoginUser;
using Workforce.Application.Mappings;
using Workforce.Application.Options;
using Workforce.Application.Services;
using Workforce.Domain.Entities;
using Workforce.Domain.Enums;
using Workforce.Domain.Repositories;
using Workforce.Shared.DTOs;
using Xunit;

namespace Workforce.Application.Tests.Features.Users.Commands.LoginUser
{
    public class LoginUserCommandHandlerTests
    {
        private readonly Mock<IUnitOfWork> _mockUnitOfWork;
        private readonly Mock<IUserRepository> _mockUserRepository;
        private readonly Mock<IPasswordHashingService> _mockPasswordHashingService;
        private readonly Mock<IJwtTokenService> _mockJwtTokenService;
        private readonly Mock<IAuditLoggingService> _mockAuditLoggingService;
        private readonly Mock<IRateLimitingService> _mockRateLimitingService;
        private readonly Mock<IOptions<AccountLockoutOptions>> _mockAccountLockoutOptions;
        private readonly IMapper _mapper;
        private readonly LoginUserCommandHandler _handler;

        public LoginUserCommandHandlerTests()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockUserRepository = new Mock<IUserRepository>();
            _mockPasswordHashingService = new Mock<IPasswordHashingService>();
            _mockJwtTokenService = new Mock<IJwtTokenService>();
            _mockAuditLoggingService = new Mock<IAuditLoggingService>();
            _mockRateLimitingService = new Mock<IRateLimitingService>();
            _mockAccountLockoutOptions = new Mock<IOptions<AccountLockoutOptions>>();

            // Setup AutoMapper
            var config = new MapperConfiguration(cfg => cfg.AddProfile<MappingProfile>());
            _mapper = config.CreateMapper();

            // Setup UnitOfWork
            _mockUnitOfWork.Setup(x => x.Users).Returns(_mockUserRepository.Object);

            // Setup default account lockout options
            var accountLockoutOptions = new AccountLockoutOptions
            {
                MaxFailedAttempts = 5,
                LockoutDurationMinutes = 15
            };
            _mockAccountLockoutOptions.Setup(x => x.Value).Returns(accountLockoutOptions);

            // Setup default rate limiting responses
            _mockRateLimitingService.Setup(x => x.IsIpRateLimitExceededAsync(It.IsAny<string>())).ReturnsAsync(false);
            _mockRateLimitingService.Setup(x => x.IsEmailRateLimitExceededAsync(It.IsAny<string>())).ReturnsAsync(false);

            _handler = new LoginUserCommandHandler(
                _mockUnitOfWork.Object,
                _mapper,
                _mockPasswordHashingService.Object,
                _mockJwtTokenService.Object,
                _mockAuditLoggingService.Object,
                _mockRateLimitingService.Object,
                _mockAccountLockoutOptions.Object);
        }

        [Fact]
        public async Task Handle_ValidCredentials_ShouldReturnAuthenticatedUserDto()
        {
            // Arrange
            var command = new LoginUserCommand
            {
                Email = "<EMAIL>",
                Password = "Password123!",
                IpAddress = "***********",
                UserAgent = "Test Agent"
            };

            var user = new User
            {
                UserId = Guid.NewGuid(),
                Email = "<EMAIL>",
                PasswordHash = "hashed_password",
                PhoneNumber = "+**********",
                FirstName = "John",
                LastName = "Doe",
                Role = UserRole.JobSeeker,
                IsActive = true,
                IsVerified = true,
                FailedLoginAttempts = 0,
                LockoutEnd = null
            };

            var accessToken = "access_token_123";
            var refreshToken = "refresh_token_123";
            var expirationSeconds = 3600;

            _mockUserRepository.Setup(x => x.GetByEmailAsync(command.Email.ToLowerInvariant())).ReturnsAsync(user);
            _mockPasswordHashingService.Setup(x => x.VerifyPassword(user.PasswordHash, command.Password)).Returns(true);
            _mockJwtTokenService.Setup(x => x.GenerateAccessToken(user)).Returns(accessToken);
            _mockJwtTokenService.Setup(x => x.GenerateRefreshToken()).Returns(refreshToken);
            _mockJwtTokenService.Setup(x => x.GetTokenExpirationSeconds()).Returns(expirationSeconds);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.User.Email.Should().Be(user.Email);
            result.AccessToken.Should().Be(accessToken);
            result.RefreshToken.Should().Be(refreshToken);
            result.TokenType.Should().Be("Bearer");
            result.ExpiresIn.Should().Be(expirationSeconds);

            _mockUserRepository.Verify(x => x.UpdateLastLoginAsync(user.UserId, It.IsAny<DateTime>()), Times.Once);
            _mockAuditLoggingService.Verify(x => x.LogSuccessfulLoginAsync(user.UserId, command.Email.ToLowerInvariant(), command.IpAddress, command.UserAgent), Times.Once);
        }

        [Fact]
        public async Task Handle_UserNotFound_ShouldThrowUnauthorizedAccessException()
        {
            // Arrange
            var command = new LoginUserCommand
            {
                Email = "<EMAIL>",
                Password = "Password123!",
                IpAddress = "***********",
                UserAgent = "Test Agent"
            };

            _mockUserRepository.Setup(x => x.GetByEmailAsync(command.Email.ToLowerInvariant())).ReturnsAsync((User?)null);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<UnauthorizedAccessException>(() => _handler.Handle(command, CancellationToken.None));
            exception.Message.Should().Be("Invalid email or password");

            _mockAuditLoggingService.Verify(x => x.LogFailedLoginAsync(command.Email.ToLowerInvariant(), "User not found", command.IpAddress, command.UserAgent), Times.Once);
        }

        [Fact]
        public async Task Handle_InvalidPassword_ShouldThrowUnauthorizedAccessExceptionAndIncrementFailedAttempts()
        {
            // Arrange
            var command = new LoginUserCommand
            {
                Email = "<EMAIL>",
                Password = "WrongPassword",
                IpAddress = "***********",
                UserAgent = "Test Agent"
            };

            var user = new User
            {
                UserId = Guid.NewGuid(),
                Email = "<EMAIL>",
                PasswordHash = "hashed_password",
                PhoneNumber = "+**********",
                FirstName = "John",
                LastName = "Doe",
                Role = UserRole.JobSeeker,
                IsActive = true,
                IsVerified = true,
                FailedLoginAttempts = 2,
                LockoutEnd = null
            };

            _mockUserRepository.Setup(x => x.GetByEmailAsync(command.Email.ToLowerInvariant())).ReturnsAsync(user);
            _mockPasswordHashingService.Setup(x => x.VerifyPassword(user.PasswordHash, command.Password)).Returns(false);
            _mockUserRepository.Setup(x => x.IncrementFailedLoginAttemptsAndGetCountAsync(user.UserId)).ReturnsAsync(3); // user.FailedLoginAttempts + 1

            // Act & Assert
            var exception = await Assert.ThrowsAsync<UnauthorizedAccessException>(() => _handler.Handle(command, CancellationToken.None));
            exception.Message.Should().Be("Invalid email or password");

            _mockUserRepository.Verify(x => x.IncrementFailedLoginAttemptsAndGetCountAsync(user.UserId), Times.Once);
            _mockAuditLoggingService.Verify(x => x.LogFailedLoginAsync(command.Email.ToLowerInvariant(), "Invalid password", command.IpAddress, command.UserAgent), Times.Once);
        }

        [Fact]
        public async Task Handle_AccountLockedOut_ShouldThrowUnauthorizedAccessException()
        {
            // Arrange
            var command = new LoginUserCommand
            {
                Email = "<EMAIL>",
                Password = "Password123!",
                IpAddress = "***********",
                UserAgent = "Test Agent"
            };

            var lockoutEnd = DateTime.UtcNow.AddMinutes(15);
            var user = new User
            {
                UserId = Guid.NewGuid(),
                Email = "<EMAIL>",
                PasswordHash = "hashed_password",
                PhoneNumber = "+**********",
                FirstName = "John",
                LastName = "Doe",
                Role = UserRole.JobSeeker,
                IsActive = true,
                IsVerified = true,
                FailedLoginAttempts = 5,
                LockoutEnd = lockoutEnd
            };

            _mockUserRepository.Setup(x => x.GetByEmailAsync(command.Email.ToLowerInvariant())).ReturnsAsync(user);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<UnauthorizedAccessException>(() => _handler.Handle(command, CancellationToken.None));
            exception.Message.Should().Contain("Account is locked out until");

            _mockAuditLoggingService.Verify(x => x.LogFailedLoginAsync(command.Email.ToLowerInvariant(), "Account locked out", command.IpAddress, command.UserAgent), Times.Once);
        }

        [Fact]
        public async Task Handle_InactiveAccount_ShouldThrowUnauthorizedAccessException()
        {
            // Arrange
            var command = new LoginUserCommand
            {
                Email = "<EMAIL>",
                Password = "Password123!",
                IpAddress = "***********",
                UserAgent = "Test Agent"
            };

            var user = new User
            {
                UserId = Guid.NewGuid(),
                Email = "<EMAIL>",
                PasswordHash = "hashed_password",
                PhoneNumber = "+**********",
                FirstName = "John",
                LastName = "Doe",
                Role = UserRole.JobSeeker,
                IsActive = false,
                IsVerified = true,
                FailedLoginAttempts = 0,
                LockoutEnd = null
            };

            _mockUserRepository.Setup(x => x.GetByEmailAsync(command.Email.ToLowerInvariant())).ReturnsAsync(user);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<UnauthorizedAccessException>(() => _handler.Handle(command, CancellationToken.None));
            exception.Message.Should().Be("Account is inactive");

            _mockAuditLoggingService.Verify(x => x.LogFailedLoginAsync(command.Email.ToLowerInvariant(), "Account inactive", command.IpAddress, command.UserAgent), Times.Once);
        }

        [Fact]
        public async Task Handle_IpRateLimitExceeded_ShouldThrowInvalidOperationException()
        {
            // Arrange
            var command = new LoginUserCommand
            {
                Email = "<EMAIL>",
                Password = "Password123!",
                IpAddress = "***********",
                UserAgent = "Test Agent"
            };

            _mockRateLimitingService.Setup(x => x.IsIpRateLimitExceededAsync(command.IpAddress)).ReturnsAsync(true);
            _mockRateLimitingService.Setup(x => x.GetIpRateLimitResetTimeAsync(command.IpAddress)).ReturnsAsync(TimeSpan.FromMinutes(5));

            // Act & Assert
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(() => _handler.Handle(command, CancellationToken.None));
            exception.Message.Should().Contain("Too many login attempts from this IP address");

            _mockAuditLoggingService.Verify(x => x.LogRateLimitExceededAsync(command.IpAddress, "IP", command.IpAddress, command.UserAgent), Times.Once);
        }

        [Fact]
        public async Task Handle_EmailRateLimitExceeded_ShouldThrowInvalidOperationException()
        {
            // Arrange
            var command = new LoginUserCommand
            {
                Email = "<EMAIL>",
                Password = "Password123!",
                IpAddress = "***********",
                UserAgent = "Test Agent"
            };

            _mockRateLimitingService.Setup(x => x.IsEmailRateLimitExceededAsync(command.Email.ToLowerInvariant())).ReturnsAsync(true);
            _mockRateLimitingService.Setup(x => x.GetEmailRateLimitResetTimeAsync(command.Email.ToLowerInvariant())).ReturnsAsync(TimeSpan.FromMinutes(3));

            // Act & Assert
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(() => _handler.Handle(command, CancellationToken.None));
            exception.Message.Should().Contain("Too many login attempts for this email");

            _mockAuditLoggingService.Verify(x => x.LogRateLimitExceededAsync(command.Email.ToLowerInvariant(), "Email", command.IpAddress, command.UserAgent), Times.Once);
        }

        [Fact]
        public async Task Handle_MaxFailedAttemptsReached_ShouldLockoutAccount()
        {
            // Arrange
            var command = new LoginUserCommand
            {
                Email = "<EMAIL>",
                Password = "WrongPassword",
                IpAddress = "***********",
                UserAgent = "Test Agent"
            };

            var user = new User
            {
                UserId = Guid.NewGuid(),
                Email = "<EMAIL>",
                PasswordHash = "hashed_password",
                PhoneNumber = "+**********",
                FirstName = "John",
                LastName = "Doe",
                Role = UserRole.JobSeeker,
                IsActive = true,
                IsVerified = true,
                FailedLoginAttempts = 4, // One more will reach the limit of 5
                LockoutEnd = null
            };

            _mockUserRepository.Setup(x => x.GetByEmailAsync(command.Email.ToLowerInvariant())).ReturnsAsync(user);
            _mockPasswordHashingService.Setup(x => x.VerifyPassword(user.PasswordHash, command.Password)).Returns(false);
            _mockUserRepository.Setup(x => x.IncrementFailedLoginAttemptsAndGetCountAsync(user.UserId)).ReturnsAsync(5); // user.FailedLoginAttempts + 1 = 5 (reaches limit)

            // Act & Assert
            var exception = await Assert.ThrowsAsync<UnauthorizedAccessException>(() => _handler.Handle(command, CancellationToken.None));
            exception.Message.Should().Be("Invalid email or password");

            _mockUserRepository.Verify(x => x.IncrementFailedLoginAttemptsAndGetCountAsync(user.UserId), Times.Once);
            _mockUserRepository.Verify(x => x.LockoutUserAsync(user.UserId, It.IsAny<DateTime>()), Times.Once);
            _mockAuditLoggingService.Verify(x => x.LogAccountLockoutAsync(user.UserId, command.Email.ToLowerInvariant(), It.IsAny<DateTime>(), command.IpAddress, command.UserAgent), Times.Once);
        }

        [Fact]
        public async Task Handle_SuccessfulLoginAfterFailedAttempts_ShouldResetFailedAttempts()
        {
            // Arrange
            var command = new LoginUserCommand
            {
                Email = "<EMAIL>",
                Password = "Password123!",
                IpAddress = "***********",
                UserAgent = "Test Agent"
            };

            var user = new User
            {
                UserId = Guid.NewGuid(),
                Email = "<EMAIL>",
                PasswordHash = "hashed_password",
                PhoneNumber = "+**********",
                FirstName = "John",
                LastName = "Doe",
                Role = UserRole.JobSeeker,
                IsActive = true,
                IsVerified = true,
                FailedLoginAttempts = 3,
                LockoutEnd = null
            };

            var accessToken = "access_token_123";
            var refreshToken = "refresh_token_123";
            var expirationSeconds = 3600;

            _mockUserRepository.Setup(x => x.GetByEmailAsync(command.Email.ToLowerInvariant())).ReturnsAsync(user);
            _mockPasswordHashingService.Setup(x => x.VerifyPassword(user.PasswordHash, command.Password)).Returns(true);
            _mockJwtTokenService.Setup(x => x.GenerateAccessToken(user)).Returns(accessToken);
            _mockJwtTokenService.Setup(x => x.GenerateRefreshToken()).Returns(refreshToken);
            _mockJwtTokenService.Setup(x => x.GetTokenExpirationSeconds()).Returns(expirationSeconds);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            _mockUserRepository.Verify(x => x.ResetFailedLoginAttemptsAsync(user.UserId), Times.Once);
            _mockUserRepository.Verify(x => x.UpdateLastLoginAsync(user.UserId, It.IsAny<DateTime>()), Times.Once);
        }
    }
}
