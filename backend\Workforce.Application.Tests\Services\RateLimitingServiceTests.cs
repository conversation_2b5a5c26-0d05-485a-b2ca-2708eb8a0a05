using FluentAssertions;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Moq;
using Workforce.Application.Options;
using Workforce.Application.Services;

namespace Workforce.Application.Tests.Services
{
    public class RateLimitingServiceTests : IDisposable
    {
        private readonly IMemoryCache _memoryCache;
        private readonly Mock<IOptions<RateLimitOptions>> _mockOptions;
        private readonly RateLimitingService _rateLimitingService;

        public RateLimitingServiceTests()
        {
            _memoryCache = new MemoryCache(new MemoryCacheOptions());
            _mockOptions = new Mock<IOptions<RateLimitOptions>>();

            // Setup default configuration values for testing
            var rateLimitOptions = new RateLimitOptions
            {
                IpMaxAttempts = 3,
                EmailMaxAttempts = 2,
                IpWindowMinutes = 1,
                EmailWindowMinutes = 1,
                CleanupIntervalMinutes = 10,
                CleanupThresholdMinutes = 5,
                EnablePeriodicCleanup = false // Disable for tests to avoid timer interference
            };

            _mockOptions.Setup(x => x.Value).Returns(rateLimitOptions);
            _rateLimitingService = new RateLimitingService(_memoryCache, _mockOptions.Object);
        }

        [Fact]
        public async Task IsIpRateLimitExceededAsync_WithNoAttempts_ShouldReturnFalse()
        {
            // Arrange
            var ipAddress = "***********";

            // Act
            var result = await _rateLimitingService.IsIpRateLimitExceededAsync(ipAddress);

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public async Task IsIpRateLimitExceededAsync_WithAttemptsUnderLimit_ShouldReturnFalse()
        {
            // Arrange
            var ipAddress = "***********";

            // Act
            await _rateLimitingService.RecordIpLoginAttemptAsync(ipAddress);
            await _rateLimitingService.RecordIpLoginAttemptAsync(ipAddress);
            var result = await _rateLimitingService.IsIpRateLimitExceededAsync(ipAddress);

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public async Task IsIpRateLimitExceededAsync_WithAttemptsAtLimit_ShouldReturnTrue()
        {
            // Arrange
            var ipAddress = "***********";

            // Act
            await _rateLimitingService.RecordIpLoginAttemptAsync(ipAddress);
            await _rateLimitingService.RecordIpLoginAttemptAsync(ipAddress);
            await _rateLimitingService.RecordIpLoginAttemptAsync(ipAddress);
            var result = await _rateLimitingService.IsIpRateLimitExceededAsync(ipAddress);

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public async Task IsEmailRateLimitExceededAsync_WithNoAttempts_ShouldReturnFalse()
        {
            // Arrange
            var email = "<EMAIL>";

            // Act
            var result = await _rateLimitingService.IsEmailRateLimitExceededAsync(email);

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public async Task IsEmailRateLimitExceededAsync_WithAttemptsUnderLimit_ShouldReturnFalse()
        {
            // Arrange
            var email = "<EMAIL>";

            // Act
            await _rateLimitingService.RecordEmailLoginAttemptAsync(email);
            var result = await _rateLimitingService.IsEmailRateLimitExceededAsync(email);

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public async Task IsEmailRateLimitExceededAsync_WithAttemptsAtLimit_ShouldReturnTrue()
        {
            // Arrange
            var email = "<EMAIL>";

            // Act
            await _rateLimitingService.RecordEmailLoginAttemptAsync(email);
            await _rateLimitingService.RecordEmailLoginAttemptAsync(email);
            var result = await _rateLimitingService.IsEmailRateLimitExceededAsync(email);

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public async Task GetIpRateLimitResetTimeAsync_WithNoAttempts_ShouldReturnZero()
        {
            // Arrange
            var ipAddress = "***********";

            // Act
            var result = await _rateLimitingService.GetIpRateLimitResetTimeAsync(ipAddress);

            // Assert
            result.Should().Be(TimeSpan.Zero);
        }

        [Fact]
        public async Task GetEmailRateLimitResetTimeAsync_WithNoAttempts_ShouldReturnZero()
        {
            // Arrange
            var email = "<EMAIL>";

            // Act
            var result = await _rateLimitingService.GetEmailRateLimitResetTimeAsync(email);

            // Assert
            result.Should().Be(TimeSpan.Zero);
        }

        [Fact]
        public async Task GetIpRateLimitResetTimeAsync_WithAttempts_ShouldReturnPositiveTime()
        {
            // Arrange
            var ipAddress = "***********";

            // Act
            await _rateLimitingService.RecordIpLoginAttemptAsync(ipAddress);
            var result = await _rateLimitingService.GetIpRateLimitResetTimeAsync(ipAddress);

            // Assert
            result.Should().BeGreaterThan(TimeSpan.Zero);
            result.Should().BeLessThanOrEqualTo(TimeSpan.FromMinutes(1));
        }

        [Fact]
        public async Task GetEmailRateLimitResetTimeAsync_WithAttempts_ShouldReturnPositiveTime()
        {
            // Arrange
            var email = "<EMAIL>";

            // Act
            await _rateLimitingService.RecordEmailLoginAttemptAsync(email);
            var result = await _rateLimitingService.GetEmailRateLimitResetTimeAsync(email);

            // Assert
            result.Should().BeGreaterThan(TimeSpan.Zero);
            result.Should().BeLessThanOrEqualTo(TimeSpan.FromMinutes(1));
        }

        [Fact]
        public async Task ConcurrentRecordIpLoginAttemptAsync_ShouldHandleRaceConditionCorrectly()
        {
            // Arrange
            var ipAddress = "***********";
            var numberOfConcurrentTasks = 10;

            // Act - Execute multiple concurrent attempts
            var tasks = Enumerable.Range(0, numberOfConcurrentTasks)
                .Select(_ => _rateLimitingService.RecordIpLoginAttemptAsync(ipAddress))
                .ToArray();

            await Task.WhenAll(tasks);

            // Assert - Check that the rate limit correctly reflects all attempts
            var isLimited = await _rateLimitingService.IsIpRateLimitExceededAsync(ipAddress);
            isLimited.Should().BeTrue("because we recorded more attempts than the limit of 3");
        }

        [Fact]
        public async Task ConcurrentRecordEmailLoginAttemptAsync_ShouldHandleRaceConditionCorrectly()
        {
            // Arrange
            var email = "<EMAIL>";
            var numberOfConcurrentTasks = 10;

            // Act - Execute multiple concurrent attempts
            var tasks = Enumerable.Range(0, numberOfConcurrentTasks)
                .Select(_ => _rateLimitingService.RecordEmailLoginAttemptAsync(email))
                .ToArray();

            await Task.WhenAll(tasks);

            // Assert - Check that the rate limit correctly reflects all attempts
            var isLimited = await _rateLimitingService.IsEmailRateLimitExceededAsync(email);
            isLimited.Should().BeTrue("because we recorded more attempts than the limit of 2");
        }

        [Fact]
        public async Task MixedConcurrentOperations_ShouldMaintainDataIntegrity()
        {
            // Arrange
            var ipAddress = "***********";
            var email = "<EMAIL>";

            // Act - Execute mixed concurrent operations
            var tasks = new List<Task>();

            // Add concurrent record operations
            for (int i = 0; i < 5; i++)
            {
                tasks.Add(_rateLimitingService.RecordIpLoginAttemptAsync(ipAddress));
                tasks.Add(_rateLimitingService.RecordEmailLoginAttemptAsync(email));
            }

            // Add concurrent check operations
            for (int i = 0; i < 3; i++)
            {
                tasks.Add(_rateLimitingService.IsIpRateLimitExceededAsync(ipAddress));
                tasks.Add(_rateLimitingService.IsEmailRateLimitExceededAsync(email));
                tasks.Add(_rateLimitingService.GetIpRateLimitResetTimeAsync(ipAddress));
                tasks.Add(_rateLimitingService.GetEmailRateLimitResetTimeAsync(email));
            }

            await Task.WhenAll(tasks);

            // Assert - Verify final state is consistent
            var ipLimited = await _rateLimitingService.IsIpRateLimitExceededAsync(ipAddress);
            var emailLimited = await _rateLimitingService.IsEmailRateLimitExceededAsync(email);

            ipLimited.Should().BeTrue("because we recorded 5 attempts which exceeds the limit of 3");
            emailLimited.Should().BeTrue("because we recorded 5 attempts which exceeds the limit of 2");
        }

        [Fact]
        public async Task EmailCaseInsensitive_ShouldTreatSameEmailConsistently()
        {
            // Arrange
            var email1 = "<EMAIL>";
            var email2 = "<EMAIL>";
            var email3 = "<EMAIL>";

            // Act
            await _rateLimitingService.RecordEmailLoginAttemptAsync(email1);
            await _rateLimitingService.RecordEmailLoginAttemptAsync(email2);
            var result = await _rateLimitingService.IsEmailRateLimitExceededAsync(email3);

            // Assert
            result.Should().BeTrue("because all email variations should be treated as the same email");
        }

        [Fact]
        public async Task TriggerCleanupAsync_WithNoSemaphores_ShouldReturnZero()
        {
            // Act
            var result = await _rateLimitingService.TriggerCleanupAsync();

            // Assert
            result.Should().Be(0);
        }

        [Fact]
        public async Task TriggerCleanupAsync_WithActiveSemaphores_ShouldNotCleanupActiveSemaphores()
        {
            // Arrange
            var ipAddress = "***********";
            var email = "<EMAIL>";

            // Create some active rate limit entries
            await _rateLimitingService.RecordIpLoginAttemptAsync(ipAddress);
            await _rateLimitingService.RecordEmailLoginAttemptAsync(email);

            // Act
            var result = await _rateLimitingService.TriggerCleanupAsync();

            // Assert
            result.Should().Be(0, "because semaphores with active cache entries should not be cleaned up");

            // Verify the rate limits are still working
            var ipLimited = await _rateLimitingService.IsIpRateLimitExceededAsync(ipAddress);
            var emailLimited = await _rateLimitingService.IsEmailRateLimitExceededAsync(email);

            ipLimited.Should().BeFalse();
            emailLimited.Should().BeFalse();
        }

        [Fact]
        public async Task MemoryLeakPrevention_SemaphoreCleanup_ShouldCleanupUnusedSemaphores()
        {
            // Arrange
            var ipAddress = "*************";

            // Create a rate limit entry
            await _rateLimitingService.RecordIpLoginAttemptAsync(ipAddress);

            // Verify it exists
            var attempts = await _rateLimitingService.IsIpRateLimitExceededAsync(ipAddress);
            attempts.Should().BeFalse();

            // Clear the cache entry manually to simulate expiration
            var key = $"ip_login_attempts:{ipAddress.Replace(":", "_").Replace(".", "_")}";
            _memoryCache.Remove(key);

            // Act - trigger cleanup
            var cleanedUp = await _rateLimitingService.TriggerCleanupAsync();

            // Assert
            cleanedUp.Should().BeGreaterThanOrEqualTo(0, "cleanup should complete without errors");
        }

        [Fact]
        public async Task ConcurrentAccess_ShouldNotCauseMemoryLeak()
        {
            // Arrange
            var tasks = new List<Task>();
            var ipAddresses = Enumerable.Range(1, 10).Select(i => $"192.168.1.{i}").ToList();

            // Act - simulate concurrent access
            foreach (var ip in ipAddresses)
            {
                tasks.Add(Task.Run(async () =>
                {
                    await _rateLimitingService.RecordIpLoginAttemptAsync(ip);
                    await _rateLimitingService.IsIpRateLimitExceededAsync(ip);
                }));
            }

            await Task.WhenAll(tasks);

            // Trigger cleanup
            var cleanedUp = await _rateLimitingService.TriggerCleanupAsync();

            // Assert
            cleanedUp.Should().BeGreaterThanOrEqualTo(0, "cleanup should handle concurrent access gracefully");
        }

        [Fact]
        public void RateLimitingService_WithPeriodicCleanupDisabled_ShouldNotStartTimer()
        {
            // Arrange
            var memoryCache = new MemoryCache(new MemoryCacheOptions());
            var mockOptions = new Mock<IOptions<RateLimitOptions>>();
            var rateLimitOptions = new RateLimitOptions
            {
                IpMaxAttempts = 3,
                EmailMaxAttempts = 2,
                IpWindowMinutes = 1,
                EmailWindowMinutes = 1,
                CleanupIntervalMinutes = 10,
                CleanupThresholdMinutes = 5,
                EnablePeriodicCleanup = false
            };
            mockOptions.Setup(x => x.Value).Returns(rateLimitOptions);

            // Act & Assert - Should not throw any exceptions
            using var service = new RateLimitingService(memoryCache, mockOptions.Object);
            service.Should().NotBeNull();

            memoryCache.Dispose();
        }

        [Fact]
        public void RateLimitingService_WithPeriodicCleanupEnabled_ShouldStartTimer()
        {
            // Arrange
            var memoryCache = new MemoryCache(new MemoryCacheOptions());
            var mockOptions = new Mock<IOptions<RateLimitOptions>>();
            var rateLimitOptions = new RateLimitOptions
            {
                IpMaxAttempts = 3,
                EmailMaxAttempts = 2,
                IpWindowMinutes = 1,
                EmailWindowMinutes = 1,
                CleanupIntervalMinutes = 1, // Short interval for testing
                CleanupThresholdMinutes = 1,
                EnablePeriodicCleanup = true
            };
            mockOptions.Setup(x => x.Value).Returns(rateLimitOptions);

            // Act & Assert - Should not throw any exceptions
            using var service = new RateLimitingService(memoryCache, mockOptions.Object);
            service.Should().NotBeNull();

            memoryCache.Dispose();
        }

        [Fact]
        public async Task TriggerCleanupAsync_WithCustomThreshold_ShouldRespectConfiguration()
        {
            // Arrange
            var memoryCache = new MemoryCache(new MemoryCacheOptions());
            var mockOptions = new Mock<IOptions<RateLimitOptions>>();
            var rateLimitOptions = new RateLimitOptions
            {
                IpMaxAttempts = 3,
                EmailMaxAttempts = 2,
                IpWindowMinutes = 1,
                EmailWindowMinutes = 1,
                CleanupIntervalMinutes = 10,
                CleanupThresholdMinutes = 1, // Very short threshold for testing
                EnablePeriodicCleanup = false
            };
            mockOptions.Setup(x => x.Value).Returns(rateLimitOptions);

            using var service = new RateLimitingService(memoryCache, mockOptions.Object);
            var ipAddress = "*************";

            // Create a rate limit entry
            await service.RecordIpLoginAttemptAsync(ipAddress);

            // Clear the cache entry to simulate expiration
            var key = $"ip_login_attempts:{ipAddress.Replace(":", "_").Replace(".", "_")}";
            memoryCache.Remove(key);

            // Wait a bit to ensure threshold is exceeded
            await Task.Delay(1100); // Wait slightly more than 1 second

            // Act
            var cleanedUp = await service.TriggerCleanupAsync();

            // Assert
            cleanedUp.Should().BeGreaterThanOrEqualTo(0, "cleanup should work with custom threshold");

            memoryCache.Dispose();
        }

        [Fact]
        public void RateLimitingService_WithDifferentConfigurationValues_ShouldUseCorrectSettings()
        {
            // Arrange
            var memoryCache = new MemoryCache(new MemoryCacheOptions());
            var mockOptions = new Mock<IOptions<RateLimitOptions>>();
            var rateLimitOptions = new RateLimitOptions
            {
                IpMaxAttempts = 15,
                EmailMaxAttempts = 8,
                IpWindowMinutes = 5,
                EmailWindowMinutes = 3,
                CleanupIntervalMinutes = 30,
                CleanupThresholdMinutes = 15,
                EnablePeriodicCleanup = false
            };
            mockOptions.Setup(x => x.Value).Returns(rateLimitOptions);

            // Act & Assert - Should not throw any exceptions and use the configured values
            using var service = new RateLimitingService(memoryCache, mockOptions.Object);
            service.Should().NotBeNull();

            memoryCache.Dispose();
        }

        public void Dispose()
        {
            _rateLimitingService?.Dispose();
            _memoryCache?.Dispose();
        }
    }
}
