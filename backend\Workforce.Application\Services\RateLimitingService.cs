using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;
using Workforce.Application.Options;

namespace Workforce.Application.Services
{
    /// <summary>
    /// Wrapper class to track semaphore usage with reference counting
    /// </summary>
    internal class SemaphoreWrapper : IDisposable
    {
        public SemaphoreSlim Semaphore { get; }
        private int _referenceCount;
        public DateTime LastUsed { get; set; }

        public SemaphoreWrapper()
        {
            Semaphore = new SemaphoreSlim(1, 1);
            _referenceCount = 0;
            LastUsed = DateTime.UtcNow;
        }

        public int ReferenceCount => _referenceCount;

        public void IncrementReference()
        {
            Interlocked.Increment(ref _referenceCount);
            LastUsed = DateTime.UtcNow;
        }

        public void DecrementReference()
        {
            Interlocked.Decrement(ref _referenceCount);
            LastUsed = DateTime.UtcNow;
        }

        public void Dispose()
        {
            Semaphore?.Dispose();
        }
    }

    /// <summary>
    /// Service for rate limiting operations using in-memory cache with thread-safe operations
    /// </summary>
    public class RateLimitingService : IRateLimitingService, IDisposable
    {
        private readonly IMemoryCache _cache;
        private readonly RateLimitOptions _options;
        private readonly int _ipMaxAttempts;
        private readonly int _emailMaxAttempts;
        private readonly TimeSpan _ipWindowDuration;
        private readonly TimeSpan _emailWindowDuration;
        private readonly TimeSpan _cleanupThreshold;

        // Thread-safe per-key locking mechanism with reference counting
        private readonly ConcurrentDictionary<string, SemaphoreWrapper> _keyLocks = new();
        private readonly SemaphoreSlim _lockCleanupSemaphore = new(1, 1);
        private readonly Timer? _cleanupTimer;
        private bool _disposed;

        public RateLimitingService(IMemoryCache cache, IOptions<RateLimitOptions> options)
        {
            _cache = cache;
            _options = options.Value;
            _ipMaxAttempts = _options.IpMaxAttempts;
            _emailMaxAttempts = _options.EmailMaxAttempts;
            _ipWindowDuration = TimeSpan.FromMinutes(_options.IpWindowMinutes);
            _emailWindowDuration = TimeSpan.FromMinutes(_options.EmailWindowMinutes);
            _cleanupThreshold = TimeSpan.FromMinutes(_options.CleanupThresholdMinutes);

            // Initialize cleanup timer if enabled
            if (_options.EnablePeriodicCleanup)
            {
                var cleanupInterval = TimeSpan.FromMinutes(_options.CleanupIntervalMinutes);
                _cleanupTimer = new Timer(_ => PeriodicCleanupAsync(), null, cleanupInterval, cleanupInterval);
            }
        }

        /// <summary>
        /// Gets or creates a semaphore for the specified key to ensure thread-safe operations
        /// </summary>
        /// <param name="key">The cache key</param>
        /// <returns>A semaphore for the key</returns>
        private SemaphoreSlim GetKeyLock(string key)
        {
            var wrapper = _keyLocks.GetOrAdd(key, _ => new SemaphoreWrapper());
            wrapper.IncrementReference();
            return wrapper.Semaphore;
        }

        /// <summary>
        /// Releases a semaphore and decrements its reference count
        /// </summary>
        /// <param name="key">The cache key</param>
        private void ReleaseKeyLock(string key)
        {
            if (_keyLocks.TryGetValue(key, out var wrapper))
            {
                wrapper.DecrementReference();
            }
        }

        /// <summary>
        /// Generates a consistent cache key for IP-based rate limiting
        /// </summary>
        /// <param name="ipAddress">The IP address</param>
        /// <returns>A consistent cache key</returns>
        private static string GetIpCacheKey(string ipAddress)
        {
            return $"ip_login_attempts:{ipAddress?.Replace(":", "_").Replace(".", "_")}";
        }

        /// <summary>
        /// Generates a consistent cache key for email-based rate limiting
        /// </summary>
        /// <param name="email">The email address</param>
        /// <returns>A consistent cache key</returns>
        private static string GetEmailCacheKey(string email)
        {
            return $"email_login_attempts:{email.ToLowerInvariant()}";
        }

        /// <summary>
        /// Safely gets and updates attempts list with thread synchronization
        /// </summary>
        /// <param name="key">The cache key</param>
        /// <param name="windowDuration">The time window for attempts</param>
        /// <param name="addNewAttempt">Whether to add a new attempt</param>
        /// <returns>The filtered attempts list</returns>
        private async Task<List<DateTime>> GetAndUpdateAttemptsAsync(string key, TimeSpan windowDuration, bool addNewAttempt = false)
        {
            var keyLock = GetKeyLock(key);
            await keyLock.WaitAsync();

            try
            {
                var attempts = _cache.Get<List<DateTime>>(key) ?? [];

                // Remove expired attempts
                var cutoff = DateTime.UtcNow.Subtract(windowDuration);
                attempts = attempts.Where(a => a > cutoff).ToList();

                // Add new attempt if requested
                if (addNewAttempt)
                {
                    attempts.Add(DateTime.UtcNow);
                }

                // Update cache with filtered/updated attempts
                _cache.Set(key, attempts, windowDuration);

                return attempts;
            }
            finally
            {
                keyLock.Release();
                ReleaseKeyLock(key);
            }
        }

        /// <summary>
        /// Checks if the IP address has exceeded the rate limit for login attempts
        /// </summary>
        /// <param name="ipAddress">The IP address to check</param>
        /// <returns>True if rate limit is exceeded, false otherwise</returns>
        public async Task<bool> IsIpRateLimitExceededAsync(string ipAddress)
        {
            if (string.IsNullOrWhiteSpace(ipAddress))
                return false;

            var key = GetIpCacheKey(ipAddress);
            var attempts = await GetAndUpdateAttemptsAsync(key, _ipWindowDuration, addNewAttempt: false);

            return attempts.Count >= _ipMaxAttempts;
        }

        /// <summary>
        /// Checks if the email has exceeded the rate limit for login attempts
        /// </summary>
        /// <param name="email">The email to check</param>
        /// <returns>True if rate limit is exceeded, false otherwise</returns>
        public async Task<bool> IsEmailRateLimitExceededAsync(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;

            var key = GetEmailCacheKey(email);
            var attempts = await GetAndUpdateAttemptsAsync(key, _emailWindowDuration, addNewAttempt: false);

            return attempts.Count >= _emailMaxAttempts;
        }

        /// <summary>
        /// Records a login attempt for IP-based rate limiting
        /// </summary>
        /// <param name="ipAddress">The IP address</param>
        public async Task RecordIpLoginAttemptAsync(string ipAddress)
        {
            if (string.IsNullOrWhiteSpace(ipAddress))
                return;

            var key = GetIpCacheKey(ipAddress);
            await GetAndUpdateAttemptsAsync(key, _ipWindowDuration, addNewAttempt: true);
        }

        /// <summary>
        /// Records a login attempt for email-based rate limiting
        /// </summary>
        /// <param name="email">The email address</param>
        public async Task RecordEmailLoginAttemptAsync(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return;

            var key = GetEmailCacheKey(email);
            await GetAndUpdateAttemptsAsync(key, _emailWindowDuration, addNewAttempt: true);
        }

        /// <summary>
        /// Gets the remaining time until the IP rate limit resets
        /// </summary>
        /// <param name="ipAddress">The IP address</param>
        /// <returns>Time remaining until reset</returns>
        public async Task<TimeSpan> GetIpRateLimitResetTimeAsync(string ipAddress)
        {
            var key = GetIpCacheKey(ipAddress);
            var attempts = await GetAndUpdateAttemptsAsync(key, _ipWindowDuration, addNewAttempt: false);

            if (attempts.Count == 0)
                return TimeSpan.Zero;

            var oldestAttempt = attempts.Min();
            var resetTime = oldestAttempt.Add(_ipWindowDuration);
            var remaining = resetTime.Subtract(DateTime.UtcNow);

            return remaining > TimeSpan.Zero ? remaining : TimeSpan.Zero;
        }

        /// <summary>
        /// Gets the remaining time until the email rate limit resets
        /// </summary>
        /// <param name="email">The email address</param>
        /// <returns>Time remaining until reset</returns>
        public async Task<TimeSpan> GetEmailRateLimitResetTimeAsync(string email)
        {
            var key = GetEmailCacheKey(email);
            var attempts = await GetAndUpdateAttemptsAsync(key, _emailWindowDuration, addNewAttempt: false);

            if (attempts.Count == 0)
                return TimeSpan.Zero;

            var oldestAttempt = attempts.Min();
            var resetTime = oldestAttempt.Add(_emailWindowDuration);
            var remaining = resetTime.Subtract(DateTime.UtcNow);

            return remaining > TimeSpan.Zero ? remaining : TimeSpan.Zero;
        }

        /// <summary>
        /// Manually triggers cleanup of unused semaphores
        /// </summary>
        /// <returns>The number of semaphores that were cleaned up</returns>
        public Task<int> TriggerCleanupAsync()
        {
            if (_disposed)
                return Task.FromResult(0);

            return Task.FromResult(CleanupUnusedSemaphores(_cleanupThreshold));
        }

        /// <summary>
        /// Periodic cleanup method called by the timer
        /// </summary>
        private Task PeriodicCleanupAsync()
        {
            if (_disposed)
                return Task.CompletedTask;

            try
            {
                CleanupUnusedSemaphores(_cleanupThreshold);
            }
            catch (Exception)
            {
                // Silently handle exceptions in background cleanup to prevent timer from stopping
                // In production, you might want to log this exception
            }

            return Task.CompletedTask;
        }

        /// <summary>
        /// Core cleanup logic that removes unused semaphores
        /// </summary>
        /// <param name="threshold">Time threshold for cleanup</param>
        /// <returns>Number of semaphores cleaned up</returns>
        private int CleanupUnusedSemaphores(TimeSpan threshold)
        {
            _lockCleanupSemaphore.Wait();
            try
            {
                var initialCount = _keyLocks.Count;
                var keysToRemove = new List<string>();
                var cutoffTime = DateTime.UtcNow.Subtract(threshold);

                foreach (var kvp in _keyLocks)
                {
                    var key = kvp.Key;
                    var wrapper = kvp.Value;

                    // Check if semaphore is unused and old
                    if (wrapper.ReferenceCount <= 0 && wrapper.LastUsed < cutoffTime)
                    {
                        // Verify cache entry doesn't exist or is empty
                        var cacheEntry = _cache.Get<List<DateTime>>(key);
                        var hasActiveCacheData = cacheEntry != null && cacheEntry.Count > 0;

                        if (!hasActiveCacheData)
                        {
                            keysToRemove.Add(key);
                        }
                    }
                }

                // Remove identified keys
                foreach (var key in keysToRemove)
                {
                    if (_keyLocks.TryRemove(key, out var wrapper))
                    {
                        wrapper.Dispose();
                    }
                }

                var finalCount = _keyLocks.Count;
                return initialCount - finalCount;
            }
            finally
            {
                _lockCleanupSemaphore.Release();
            }
        }

        /// <summary>
        /// Disposes of the service and cleans up resources
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

                // Dispose cleanup timer
                _lockCleanupSemaphore?.Dispose();
        /// Protected dispose method for proper disposal pattern
        /// </summary>
        /// <param name="disposing">Whether disposing is called from Dispose method</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                // Dispose cleanup timer
                _cleanupTimer?.Dispose();

                // Dispose all semaphores
                foreach (var semaphore in _keyLocks.Values)
                {
                    semaphore?.Dispose();
                }
                _keyLocks.Clear();

                _lockCleanupSemaphore?.Dispose();
                _disposed = true;
            }
        }
    }
}
